This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.8.4)  8 AUG 2025 11:03
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**report_1sal_ekoru.tex
(./report_1sal_ekoru.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks16
\inpenc@posthook=\toks17
)
(/usr/share/texlive/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2022/01/26 3.70 The Babel package
\babel@savecnt=\count193
\U@D=\dimen139
\l@unhyphenated=\language8

(/usr/share/texlive/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count194

(/usr/share/texlive/texmf-dist/tex/generic/babel-italian/italian.ldf
Language: italian 2020/05/21 v.1.4.04 Italian support for the babel system
\it@lettering=\count195
\it@doublequoteactive=\count196
\it@ISOcompliance=\count197
))
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count198
)
(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\Gm@cnth=\count199
\Gm@cntv=\count266
\c@Gm@tempcnt=\count267
\Gm@bindingoffset=\dimen140
\Gm@wd@mp=\dimen141
\Gm@odd@mp=\dimen142
\Gm@even@mp=\dimen143
\Gm@layoutwidth=\dimen144
\Gm@layoutheight=\dimen145
\Gm@layouthoffset=\dimen146
\Gm@layoutvoffset=\dimen147
\Gm@dimlist=\toks19
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen148
\Gin@req@width=\dimen149
)
(/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2021/01/28 v4.0.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip49
\f@nch@O@elh=\skip50
\f@nch@O@erh=\skip51
\f@nch@O@olh=\skip52
\f@nch@O@orh=\skip53
\f@nch@O@elf=\skip54
\f@nch@O@erf=\skip55
\f@nch@O@olf=\skip56
\f@nch@O@orf=\skip57
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen150
\ar@mcellbox=\box50
\extrarowheight=\dimen151
\NC@list=\toks20
\extratabsurround=\skip58
\backup@length=\skip59
\ar@cellbox=\box51
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2021-09-01 v4.17 Multi-page Table package (DPC)
\LTleft=\skip60
\LTright=\skip61
\LTpre=\skip62
\LTpost=\skip63
\LTchunksize=\count268
\LTcapwidth=\dimen152
\LT@head=\box52
\LT@firsthead=\box53
\LT@foot=\box54
\LT@lastfoot=\box55
\LT@gbox=\box56
\LT@cols=\count269
\LT@rows=\count270
\c@LT@tables=\count271
\c@LT@chunks=\count272
\LT@p@ftn=\toks21
)
(/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen153
\lightrulewidth=\dimen154
\cmidrulewidth=\dimen155
\belowrulesep=\dimen156
\belowbottomsep=\dimen157
\aboverulesep=\dimen158
\abovetopsep=\dimen159
\cmidrulesep=\dimen160
\cmidrulekern=\dimen161
\defaultaddspace=\dimen162
\@cmidla=\count273
\@cmidlb=\count274
\@aboverulesep=\dimen163
\@belowrulesep=\dimen164
\@thisruleclass=\count275
\@lastruleclass=\count276
\@thisrulewidth=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip64
\multirow@cntb=\count277
\multirow@dima=\skip65
\bigstrutjot=\dimen166
)
(/usr/share/texlive/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2020/01/04 v1.0e Color table columns (DPC)
\everycr=\toks22
\minrowclearance=\skip66
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip67

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen167
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen168
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count278
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count279
\leftroot@=\count280
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count281
\DOTSCASE@=\count282
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box57
\strutbox@=\box58
\big@size=\dimen169
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count283
\c@MaxMatrixCols=\count284
\dotsspace@=\muskip16
\c@parentequation=\count285
\dspbrk@lvl=\count286
\tag@help=\toks24
\row@=\count287
\column@=\count288
\maxfields@=\count289
\andhelp@=\toks25
\eqnshift@=\dimen170
\alignsep@=\dimen171
\tagshift@=\dimen172
\tagwidth@=\dimen173
\totwidth@=\dimen174
\lineht@=\dimen175
\@envbody=\toks26
\multlinegap=\skip68
\multlinetaggap=\skip69
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip70
\enit@outerparindent=\dimen176
\enit@toks=\toks28
\enit@inbox=\box59
\enit@count@id=\count290
\enitdp@description=\count291
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen177
\pgfutil@tempdimb=\dimen178

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box60
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
))
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen179
\pgf@y=\dimen180
\pgf@xa=\dimen181
\pgf@ya=\dimen182
\pgf@xb=\dimen183
\pgf@yb=\dimen184
\pgf@xc=\dimen185
\pgf@yc=\dimen186
\pgf@xd=\dimen187
\pgf@yd=\dimen188
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count292
\c@pgf@countb=\count293
\c@pgf@countc=\count294
\c@pgf@countd=\count295
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count296
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count297
\pgfsyssoftpath@bigbuffer@items=\count298
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen189
\pgfmath@count=\count299
\pgfmath@box=\box61
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count300
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen190
\pgf@picmaxx=\dimen191
\pgf@picminy=\dimen192
\pgf@picmaxy=\dimen193
\pgf@pathminx=\dimen194
\pgf@pathmaxx=\dimen195
\pgf@pathminy=\dimen196
\pgf@pathmaxy=\dimen197
\pgf@xx=\dimen198
\pgf@xy=\dimen199
\pgf@yx=\dimen256
\pgf@yy=\dimen257
\pgf@zx=\dimen258
\pgf@zy=\dimen259
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen260
\pgf@path@lasty=\dimen261
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen262
\pgf@shorten@start@additional=\dimen263
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box62
\pgf@hbox=\box63
\pgf@layerbox@main=\box64
\pgf@picture@serial@count=\count301
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen264
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen265
\pgf@pt@y=\dimen266
\pgf@pt@temp=\dimen267
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen268
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen269
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box65
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box66
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen270
\pgf@nodesepend=\dimen271
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen272
\pgffor@skip=\dimen273
\pgffor@stack=\toks39
\pgffor@toks=\toks40
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count304
\pgfplotmarksize=\dimen274
)
\tikz@lastx=\dimen275
\tikz@lasty=\dimen276
\tikz@lastxsaved=\dimen277
\tikz@lastysaved=\dimen278
\tikz@lastmovetox=\dimen279
\tikz@lastmovetoy=\dimen280
\tikzleveldistance=\dimen281
\tikzsiblingdistance=\dimen282
\tikz@figbox=\box67
\tikz@figbox@bg=\box68
\tikz@tempbox=\box69
\tikz@tempbox@bg=\box70
\tikztreelevel=\count305
\tikznumberofchildren=\count306
\tikznumberofcurrentchild=\count307
\tikz@fig@count=\count308

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count309
\pgfmatrixcurrentcolumn=\count310
\pgf@matrix@numberofcolumns=\count311
)
\tikz@expandcount=\count312

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2022/01/07 version 5.0.2 text color boxes

(/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2020-07-07 v1.5u LaTeX2e package for verbatim enhancements
\every@verbatim=\toks41
\verbatim@line=\toks42
\verbatim@in@stream=\read4
)
(/usr/share/texlive/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box71
\tcb@upperbox=\box72
\tcb@lowerbox=\box73
\tcb@phantombox=\box74
\c@tcbbreakpart=\count313
\c@tcblayer=\count314
\c@tcolorbox@number=\count315
\tcb@temp=\box75
\tcb@temp=\box76
\tcb@temp=\box77
\tcb@temp=\box78
)
(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2022-01-21 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count316
\l__pdf_internal_box=\box79
))
Package: fontawesome5 2021/06/04 v5.15.3 Font Awesome 5

(/usr/share/texlive/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2022-01-12 LaTeX2e option processing using LaTeX3 keys
)
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2022-01-12 L3 Experimental document command parser
)
(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5-generic-help
er.sty
Package: fontawesome5-generic-helper 2021/06/04 v5.15.3 non-uTeX helper for fon
tawesome5

(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5-mapping.def)
)) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-06-07 v7.00m Hypertext links for LaTeX

(/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(/usr/share/texlive/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen283
\Hy@linkcounter=\count317
\Hy@pagecounter=\count318

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-06-07 v7.00m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-06-07 v7.00m Hyperref: patches for babel la
nguages
)
(/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count319

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-06-07 v7.00m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count320

(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen284

(/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count321
\Field@Width=\dimen285
\Fld@charsize=\dimen286
Package hyperref Info: Hyper figures OFF on input line 6076.
Package hyperref Info: Link nesting OFF on input line 6081.
Package hyperref Info: Hyper index ON on input line 6084.
Package hyperref Info: backreferencing OFF on input line 6091.
Package hyperref Info: Link coloring OFF on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6101.
Package hyperref Info: PDF/A mode OFF on input line 6106.
LaTeX Info: Redefining \ref on input line 6146.
LaTeX Info: Redefining \pageref on input line 6150.

(/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count322
\c@Item=\count323
\c@Hfootnote=\count324
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-06-07 v7.00m Hyperref driver for pdfTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count325
\c@bookmark@seq@number=\count326

(/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)

(/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
86.
)
\Hy@SectionHShift=\skip71
)
(/usr/share/texlive/texmf-dist/tex/latex/eurosym/eurosym.sty
Package: eurosym 1998/08/06 v1.1 European currency symbol ``Euro''
\@eurobox=\box80
)
(/usr/share/texlive/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2022/01/11 v1.705(Michael Sharpe) latex and unicode latex su
pport for TeXGyreTermesX

`newtxtext' v1.705, 2022/01/11 Text macros taking advantage of TeXGyre Termes a
nd its extensions (msharpe)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
(/usr/share/texlive/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2020/11/20 v2.8 package option processing (HA)

(/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks43
\XKV@tempa@toks=\toks44
)
\XKV@depth=\count327
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.sty
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.tex
\integerpart=\count328
\decimalpart=\count329
)
Package: xstring 2021/07/21 v1.84 String manipulations (CT)
)
(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 27.

(/usr/share/texlive/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count330
\ntx@cnt=\count331

(/usr/share/texlive/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
LaTeX Info: Redefining \textsu on input line 541.
LaTeX Info: Redefining \oldstylenums on input line 631.
)
(/usr/share/texlive/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2021/12/18 v1.7

`newtxmath' v1.7, 2021/12/18 Math macros based originally on txfonts (msharpe)
(/usr/share/texlive/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count332

(/usr/share/texlive/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count333
\tx@IsAlNum=\count334
\tx@tA=\toks45
\tx@tB=\toks46
\tx@su=\read5

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ntxtlf/m/n on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/m/n on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/ntxtlf/m/n --> OT1/ntxtlf/b/n on input line 374.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 380.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/qhv/m/n on input line 380.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> T1/qhv/m/n on input line 380.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 381.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ntxtlf/m/it on input line 381.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ntxtlf/m/it on input line 381.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 382.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> T1/ntxtt/m/n on input line 382.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> T1/ntxtt/m/n on input line 382.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/b/n on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/b/n on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/ntxtlf/m/it --> OT1/ntxtlf/b/it on input line 385.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/qhv/m/n --> T1/qhv/b/n on input line 386.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/ntxtt/m/n --> T1/ntxtt/b/n on input line 387.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 489.
\symlettersA=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 544.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 546.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 565.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 565.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 566.
\symAMSm=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 591.
\symsymbolsC=\mathgroup8
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 612.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 625.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
25.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 625.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 625
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 625.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 626.
\symlargesymbolsTXA=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 640.
\tx@sbptoks=\toks47
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 863.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 864.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 865.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 866.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 867.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 868.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 869.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 871.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 875.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 876.
LaTeX Font Info:    Redeclaring math accent \dot on input line 947.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 948.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2017.
LaTeX Info: Redefining \Bbbk on input line 2807.
LaTeX Info: Redefining \not on input line 2956.
) (/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+ntxtlf on input line
 112.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/t1ntxtlf.fd
File: t1ntxtlf.fd 2021/05/24 v1.1 font definition file for T1/ntx/tlf
)
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 112.
)
(./report_1sal_ekoru.aux)
\openout1 = `report_1sal_ekoru.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 69.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 69.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 69.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
69.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 69.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 69.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 69.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 69.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 69.
LaTeX Info: Redefining \it@ocap on input line 69.
LaTeX Info: Redefining \it@ccap on input line 69.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(85.35826pt, 674.33032pt, 85.35826pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=674.33032pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-58.94173pt
* \headheight=47.03pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count335
\scratchdimen=\dimen287
\scratchbox=\box81
\nofMPsegments=\count336
\nofMParguments=\count337
\everyMPshowfont=\toks48
\MPscratchCnt=\count338
\MPscratchDim=\dimen288
\MPnumerator=\count339
\makeMPintoPDFobject=\count340
\everyMPtoPDFconversion=\toks49
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(/usr/share/texlive/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package hyperref Info: Link coloring OFF on input line 69.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section

(/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count341
)
LaTeX Info: Redefining \ref on input line 69.
LaTeX Info: Redefining \pageref on input line 69.
LaTeX Info: Redefining \nameref on input line 69.

(./report_1sal_ekoru.out) (./report_1sal_ekoru.out)
\@outlinefile=\write4
\openout4 = `report_1sal_ekoru.out'.

\c@mv@tabular=\count342
\c@mv@boldtabular=\count343
<assets/client_logo.jpg, id=103, 243.15843pt x 85.06781pt>
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 86.
(pdftex.def)             Requested size: 364.19667pt x 127.41318pt.
<assets/logo.png, id=104, 376.40625pt x 376.40625pt>
File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 88.
(pdftex.def)             Requested size: 113.81102pt x 113.81297pt.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 24.88pt on input line 101.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 24.88pt on input line 101.
LaTeX Font Info:    Trying to load font information for TS1+ntxtlf on input lin
e 101.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/ts1ntxtlf.fd
File: ts1ntxtlf.fd 2015/01/18 v1.0 fd file for TS1/ntxtlf
)
LaTeX Font Info:    Font shape `TS1/ntxtlf/b/n' will be
(Font)              scaled to size 24.88pt on input line 101.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 20.74pt on input line 102.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 20.74pt on input line 102.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 17.28pt on input line 103.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 17.28pt on input line 103.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 14.4pt on input line 104.
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input lin
e 119.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 119.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.8pt on input line 119.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 6.6pt on input line 119.
LaTeX Font Info:    Trying to load font information for U+msa on input line 119
.

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 119
.

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
119.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2018/04/14 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
119.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2015/03/20 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
119.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 120.
LaTeX Font Info:    Font shape `U/eurosym/b/n' in size <17.28> not available
(Font)              Font shape `U/eurosym/bx/n' tried instead on input line 125
.
 [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map} <./assets/client_logo.jpg> 
<./assets/logo.png>]
File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 137.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 137.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
 [2] (./report_1sal_ekoru.toc)
\tf@toc=\write5
\openout5 = `report_1sal_ekoru.toc'.

File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 143.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 143.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.143 \newpage
               [1]
LaTeX Font Info:    Font shape `TS1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 147.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 14.4pt on input line 149.
LaTeX Font Info:    Font shape `TS1/ntxtlf/b/n' will be
(Font)              scaled to size 17.28pt on input line 154.
LaTeX Font Info:    Font shape `U/eurosym/b/n' in size <12> not available
(Font)              Font shape `U/eurosym/bx/n' tried instead on input line 157
.
LaTeX Font Info:    Trying to load font information for U+fontawesomefree0 on i
nput line 172.

(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/ufontawesomefree0.fd)
LaTeX Font Info:    Trying to load font information for U+fontawesomefree1 on i
nput line 173.

(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/ufontawesomefree1.fd)
LaTeX Font Info:    Trying to load font information for U+fontawesomefree3 on i
nput line 176.

(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/ufontawesomefree3.fd)
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 8.0pt on input line 202.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 8.0pt on input line 206.

Overfull \hbox (2.30478pt too wide) in paragraph at lines 208--208
[]|\T1/ntxtlf/m/n/8 04/10/2024| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 210--210
[]|\T1/ntxtlf/m/n/8 17/02/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 212--212
[]|\T1/ntxtlf/m/n/8 07/03/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 214--214
[]|\T1/ntxtlf/m/n/8 07/05/2025| 
 []

LaTeX Font Info:    Font shape `U/eurosym/b/n' in size <8> not available
(Font)              Font shape `U/eurosym/bx/n' tried instead on input line 217
.

Overfull \hbox (9.57065pt too wide) in alignment at lines 203--219
 [] [] [] [] [] [] [] [] [] 
 []

File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 219.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 219.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
pdfTeX warning (ext4): destination with the same identifier (name{page.2}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.219 \end{longtable}
                      [2]
Overfull \hbox (5.1498pt too wide) in paragraph at lines 230--230
[]|\T1/ntxtlf/m/n/8 27/09/2024| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 230--230
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 1.750.000,00| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 230--230
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 1.750.000,00| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 230--230
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 1.750.000,00| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 232--232
[]|\T1/ntxtlf/m/n/8 09/01/2025| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 232--232
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 2.000.000,00| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 232--232
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 2.000.000,00| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 232--232
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 2.000.000,00| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 234--234
[]|\T1/ntxtlf/m/n/8 03/02/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 236--236
[]|\T1/ntxtlf/m/n/8 04/02/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 238--238
[]|\T1/ntxtlf/m/n/8 07/04/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 240--240
[]|\T1/ntxtlf/m/n/8 03/06/2025| 
 []


Overfull \hbox (7.10487pt too wide) in paragraph at lines 242--242
[]|\T1/ntxtlf/m/n/8 PR2024112501| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 242--242
[]|\T1/ntxtlf/m/n/8 25/11/2024| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 244--244
[]|\T1/ntxtlf/m/n/8 27/03/2025| 
 []


Underfull \hbox (badness 10000) in paragraph at lines 246--246
[]|\T1/ntxtlf/m/n/8 TEKNOMONT
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 246--246
[]|\T1/ntxtlf/m/n/8 28/01/2025| 
 []


Underfull \hbox (badness 10000) in paragraph at lines 248--248
[]|\T1/ntxtlf/m/n/8 TEKNOMONT
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 248--248
[]|\T1/ntxtlf/m/n/8 31/03/2025| 
 []


Underfull \hbox (badness 10000) in paragraph at lines 250--250
[]|\T1/ntxtlf/m/n/8 TEKNOMONT
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 250--250
[]|\T1/ntxtlf/m/n/8 30/04/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 252--252
[]|\T1/ntxtlf/m/n/8 10/02/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 254--254
[]|\T1/ntxtlf/m/n/8 22/04/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 256--256
[]|\T1/ntxtlf/m/n/8 18/03/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 258--258
[]|\T1/ntxtlf/m/n/8 23/04/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 260--260
[]|\T1/ntxtlf/m/n/8 25/03/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 262--262
[]|\T1/ntxtlf/m/n/8 15/04/2025| 
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 264--264
[]|\T1/ntxtlf/m/n/8 07/05/2025| 
 []


Underfull \hbox (badness 4899) in paragraph at lines 266--266
[]|\T1/ntxtlf/m/n/8 SEW EU-RO-DRI-
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 266--266
[]|\T1/ntxtlf/m/n/8 21/05/2025| 
 []


Underfull \hbox (badness 4899) in paragraph at lines 268--268
[]|\T1/ntxtlf/m/n/8 SEW EU-RO-DRI-
 []


Overfull \hbox (5.1498pt too wide) in paragraph at lines 268--268
[]|\T1/ntxtlf/m/n/8 30/05/2025| 
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 271--271
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.233.262,49| 
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 271--271
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.398.525,03| 
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 271--271
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.233.262,49| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 284--284
[]|\T1/ntxtlf/m/n/8 15/04/2025| 
 []


Overfull \hbox (9.57065pt too wide) in alignment at lines 279--289
 [] [] [] [] [] [] [] [] [] 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 300--300
[]|\T1/ntxtlf/m/n/8 12/12/2024| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 302--302
[]|\T1/ntxtlf/m/n/8 07/05/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 304--304
[]|\T1/ntxtlf/m/n/8 14/01/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 306--306
[]|\T1/ntxtlf/m/n/8 28/04/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 308--308
[]|\T1/ntxtlf/m/n/8 25/07/2024| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 310--310
[]|\T1/ntxtlf/m/n/8 31/03/2025| 
 []


Overfull \hbox (2.30478pt too wide) in paragraph at lines 312--312
[]|\T1/ntxtlf/m/n/8 28/02/2025| 
 []


Overfull \hbox (9.57065pt too wide) in alignment at lines 295--319
 [] [] [] [] [] [] [] [] [] 
 []

File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 319.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 319.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
[3]
Overfull \hbox (2.30478pt too wide) in paragraph at lines 330--330
[]|\T1/ntxtlf/m/n/8 27/05/2024| 
 []


Overfull \hbox (9.57065pt too wide) in alignment at lines 325--335
 [] [] [] [] [] [] [] [] [] 
 []

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.0pt on input line 345.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 6.2pt on input line 345.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 5.5pt on input line 345.

Overfull \hbox (5.1376pt too wide) in paragraph at lines 352--352
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 6.233.262,49| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 352--352
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 6.398.525,03| 
 []


Overfull \hbox (5.1376pt too wide) in paragraph at lines 352--352
[]|\U/eurosym/m/n/8 e\T1/ntxtlf/m/n/8 6.233.262,49| 
 []


Underfull \hbox (badness 10000) in paragraph at lines 354--354
[]|\T1/ntxtlf/m/n/8 A.4) Pro-gram-mi
 []


Underfull \hbox (badness 10000) in paragraph at lines 361--361
[]|\T1/ntxtlf/b/n/8 TOTALE PRO-
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 361--361
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.619.721,99| 
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 361--361
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.846.540,86| 
 []


Overfull \hbox (5.05827pt too wide) in paragraph at lines 361--361
[]|\U/eurosym/bx/n/8 e\T1/ntxtlf/b/n/8 6.619.721,99| 
 []

LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 374.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 378.

Overfull \hbox (3.668pt too wide) in alignment at lines 375--391
 [] [] [] [] [] [] 
 []


Underfull \hbox (badness 2495) in paragraph at lines 410--411
[]|\T1/ntxtlf/m/n/10 Fattura e pa-ga-
 []

File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 445.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 445.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
[4]
Overfull \hbox (3.668pt too wide) in alignment at lines 451--461
 [] [] [] [] [] [] 
 []


Underfull \hbox (badness 2809) in paragraph at lines 486--487
[]|\T1/ntxtlf/m/n/10 Ordine man-
 []


Overfull \hbox (3.668pt too wide) in alignment at lines 467--491
 [] [] [] [] [] [] 
 []


Overfull \hbox (3.668pt too wide) in alignment at lines 497--507
 [] [] [] [] [] [] 
 []

File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 507.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 507.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
[5]
File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 554.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 554.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
 [6]
File: assets/logo.png Graphic file (type png)
<use assets/logo.png>
Package pdftex.def Info: assets/logo.png  used on input line 584.
(pdftex.def)             Requested size: 42.67412pt x 42.67912pt.
File: assets/client_logo.jpg Graphic file (type jpg)
<use assets/client_logo.jpg>
Package pdftex.def Info: assets/client_logo.jpg  used on input line 584.
(pdftex.def)             Requested size: 121.99446pt x 42.67912pt.
 [7] (./report_1sal_ekoru.aux)

Package rerunfilecheck Warning: File `report_1sal_ekoru.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `report_1sal_ekoru.out':
(rerunfilecheck)             Before: 510645C27C266114FDF0DB8381C98B2A;5237
(rerunfilecheck)             After:  4D6BAF04B63FAB9A34A1E12CA85FFA2C;4700.
 ) 
Here is how much of TeX's memory you used:
 31075 strings out of 480202
 580749 string characters out of 5894252
 913693 words of memory out of 5000000
 48453 multiletter control sequences out of 15000+600000
 539208 words of font info for 119 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 84i,15n,80p,420b,612s stack positions out of 5000i,500n,10000p,200000b,80000s
pdfTeX warning (dest): name{subsection.5.2} has been referenced but does not 
exist, replaced by a fixed one

pdfTeX warning (dest): name{subsection.4.3} has been referenced but does not ex
ist, replaced by a fixed one

pdfTeX warning (dest): name{subsection.4.2} has been referenced but does not ex
ist, replaced by a fixed one

{/usr/share/texlive/texmf-dist/fonts/enc/dvips/fontawesome5/fa5free3.enc}{/usr/
share/texlive/texmf-dist/fonts/enc/dvips/fontawesome5/fa5free1.enc}{/usr/share/
texlive/texmf-dist/fonts/enc/dvips/fontawesome5/fa5free0.enc}{/usr/share/texmf/
fonts/enc/dvips/tex-gyre/q-ts1.enc}{/usr/share/texlive/texmf-dist/fonts/enc/dvi
ps/newtx/ntx-ec-tlf.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/fonta
wesome5/FontAwesome5Free-Solid.pfb></usr/share/texlive/texmf-dist/fonts/type1/p
ublic/eurosym/feybr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/eur
osym/feymr10.pfb></usr/share/texmf/fonts/type1/public/tex-gyre/qtmb.pfb></usr/s
hare/texmf/fonts/type1/public/tex-gyre/qtmr.pfb></usr/share/texlive/texmf-dist/
fonts/type1/public/newtx/ztmb.pfb></usr/share/texlive/texmf-dist/fonts/type1/pu
blic/newtx/ztmr.pfb>
Output written on report_1sal_ekoru.pdf (9 pages, 277896 bytes).
PDF statistics:
 246 PDF objects out of 1000 (max. 8388607)
 213 compressed objects within 3 object streams
 47 named destinations out of 1000 (max. 500000)
 215 words of extra memory for PDF output out of 10000 (max. 10000000)

